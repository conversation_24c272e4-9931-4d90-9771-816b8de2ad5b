services:
  php:
    user: '${USER_ID}:${GROUP_ID}'
    build: ./docker/php
    volumes:
      - .:/var/www:delegated
    depends_on:
      database:
        condition: service_healthy
    networks:
      - app_network

  nginx:
    build: ./docker/nginx
    ports:
     - "8000:80"
    volumes:
      - ./public:/var/www/public:delegated
    depends_on:
     - php
    networks:
      - app_network           

  database:
    image: mariadb:10.7.3
    environment:
      MARIADB_USER: root
      MARIADB_ROOT_PASSWORD: root
      MARIADB_DATABASE: BookApi
      MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: 'no'
    volumes:
      - database_data:/var/lib/mysql:rw
      - ./var/mysql:/var/www/var
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app_network

  adminer:
    image: adminer:latest
    depends_on:
      - database
    environment:
      APP_ENV: dev
      ADMINER_DESIGN: pepa-linha
      ADMINER_DEFAULT_SERVER: 
    ports:
      - "8082:8080"
    networks:
      - app_network
      
  mailer:
    image: axllent/mailpit
    ports:
      - "1025:1025"
      - "8025:8025"
    environment:
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
    networks:
      - app_network          

networks:
  app_network:

volumes:
  database_data:      