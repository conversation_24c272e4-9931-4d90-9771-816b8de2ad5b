<?php

namespace App\BookStore\Infrastructure\Controller;

use App\BookStore\Application\Command\CreateBookCommand;
use App\BookStore\Application\Query\FindBookQuery as GetBookQuery;
use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;
use App\Shared\Application\Command\CommandBusInterface;
use App\Shared\Application\Query\QueryBusInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Uid\Uuid;

class BookController
{
    public function __construct(
        private readonly CommandBusInterface $commandBus,
        private readonly QueryBusInterface $queryBus
    ) {
    }

    #[Route('/books', methods: ['POST'])]
    public function create(Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        $book = $this->commandBus->dispatch(new CreateBookCommand(
            new BookName($data['name']),
            new BookDescription($data['description']),
            new Author($data['author']),
            new BookContent($data['content']),
            new Price((int)($data['price'] * 100))
        ));

        return new JsonResponse([
            'id' => $book->getId(),
            'name' => $book->getName(),
            // autres propriétés...
        ], Response::HTTP_CREATED);
    }

    #[Route('/books', methods: ['GET'])]
    public function getBook(Uuid $id): Response
    {
        $book = $this->queryBus->ask(new GetBookQuery(BookId::fromString((string) $id)));

        return new JsonResponse([
            'id' => $book->getId(),
            'name' => $book->getName(),
            'description' => $book->getDescription(),
            'author' => $book->getAuthor(),
            'price' => $book->getPrice() / 100
        ]);
    }
}
