<?php

declare(strict_types=1);

namespace App\BookStore\Infrastructure\Doctrine;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Domain\ValueObject\Author;

use App\BookStore\Domain\ValueObject\BookId;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;

final class DoctrineBookRepository implements BookRepositoryInterface
{
    private const ENTITY_CLASS = Book::class;
    private const ALIAS = 'book';

    private QueryBuilder $queryBuilder;

    public function __construct(protected EntityManagerInterface $em)
    {
        $this->queryBuilder = $this->em->createQueryBuilder()
            ->select(self::ALIAS)
            ->from(self::ENTITY_CLASS, self::ALIAS);
    }

    public function add(Book $book): void
    {
        $this->em->persist($book);
        $this->em->flush();
    }

    public function remove(Book $book): void
    {
        $this->em->remove($book);
    }

    public function ofId(BookId $id): ?Book
    {
        return $this->em->find(self::ENTITY_CLASS, $id);
    }


    public function withAuthor(Author $author): static
    {
        $this->queryBuilder->andWhere(self::ALIAS . '.author = :author')
            ->setParameter('author', $author);

        return $this;
    }

    public function getIterator(): \Iterator
    {
        return (new Paginator($this->queryBuilder->getQuery()))->getIterator();
    }

    public function count(): int
    {
        return (new Paginator($this->queryBuilder->getQuery()))->count();
    }
}
