<?php

namespace App\BookStore\Infrastructure\Doctrine;

use App\BookStore\Domain\Repository\BookRepository;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Student>
 */
class DoctrineBookRepositoryv2 extends ServiceEntityRepository implements BookRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Student::class);
    }

    public function add(Student $student): void
    {
        $this->getEntityManager()->persist($student);
        $this->getEntityManager()->flush();
    }

    public function remove(Student $student): void
    {
        $this->getEntityManager()->remove($student);
        $this->getEntityManager()->flush();
    }

    public function getById(int $studentId): Student
    {
        $student = $this->find($studentId);

        if ($student === null) {
            throw StudentNotFound::withId($studentId);
        }

        return $student;
    }

    public function getByEmail(Email $email): ?Student
    {
        return $this->findOneBy([
            'email.value' => $email->value,
        ]);
    }
}
