<?php

declare(strict_types=1);

namespace App\BookStore\Infrastructure\ApiPlatform\Resource;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Post;
use App\BookStore\Domain\Model\Book;
use App\BookStore\Infrastructure\ApiPlatform\State\Processor\CreateBookProcessor;
use App\BookStore\Infrastructure\ApiPlatform\State\Provider\BookCollectionProvider;
use App\BookStore\Infrastructure\ApiPlatform\State\Provider\BookItemProvider;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    shortName: 'Book',
    operations: [
        // basic crud
        new GetCollection(
            provider: BookCollectionProvider::class,
        ),
        new Get(
            provider: BookItemProvider::class,
        ),
        new Post(
            validationContext: ['groups' => ['create']],
            processor: CreateBookProcessor::class,
        ),
    ],
    validationContext: ['groups' => ['Default', 'create']]
)]
final class BookResource
{
    public function __construct(
        #[ApiProperty(identifier: true, readable: true, writable: false)]
        public ?string $id = null,
        #[Assert\NotNull(groups: ['create'])]
        #[Assert\Length(min: 1, max: 255, groups: ['create', 'Default'])]
        public ?string $name = null,
        #[Assert\NotNull(groups: ['create'])]
        #[Assert\Length(min: 1, max: 1023, groups: ['create', 'Default'])]
        public ?string $description = null,
        #[Assert\NotNull(groups: ['create'])]
        #[Assert\Length(min: 1, max: 255, groups: ['create', 'Default'])]
        public ?string $author = null,
        #[Assert\NotNull(groups: ['create'])]
        #[Assert\Length(min: 1, max: 65535, groups: ['create', 'Default'])]
        public ?string $content = null,
        #[Assert\NotNull(groups: ['create'])]
        #[Assert\Range(min: 0, groups: ['create', 'Default'])]

        // Or use a combination of constraints
        #[Assert\Type('numeric')]
        #[Assert\GreaterThanOrEqual(0)]
        public ?int $price = null,
    ) {
    }

    public static function fromModel(Book $book): self
    {
        return new self(
            $book->id()->__toString(),
            $book->name()->value,
            $book->description()->value,
            $book->author()->value,
            $book->content()->value,
            $book->price()->amount,
        );
    }
}
