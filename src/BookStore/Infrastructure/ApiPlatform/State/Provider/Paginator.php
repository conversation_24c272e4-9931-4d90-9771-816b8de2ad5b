<?php

declare(strict_types=1);

namespace App\BookStore\Infrastructure\ApiPlatform\State\Provider;

use Traversable;

/**
 * @template T
 * @implements \IteratorAggregate<T>
 */
final class Paginator implements \Countable, \IteratorAggregate
{
    /**
     * @param iterable<T> $items
     */
    public function __construct(
        private iterable $items,
        private int $totalItems,
        private int $currentPage,
        private int $itemsPerPage
    ) {
    }

    public function count(): int
    {
        return $this->totalItems;
    }

    /**
     * @return Traversable<T>
     */
    public function getIterator(): Traversable
    {
        return new \ArrayIterator($this->items instanceof Traversable ? iterator_to_array($this->items) : $this->items);
    }

    public function getTotalItems(): int
    {
        return $this->totalItems;
    }

    public function getCurrentPage(): int
    {
        return $this->currentPage;
    }

    public function getItemsPerPage(): int
    {
        return $this->itemsPerPage;
    }

    public function getLastPage(): float
    {
        if (0 >= $this->itemsPerPage) {
            return 1.;
        }

        return ceil($this->totalItems / $this->itemsPerPage);
    }
}
