<?php

declare(strict_types=1);

namespace App\BookStore\Infrastructure\ApiPlatform\State\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\BookStore\Application\Query\FindBookQuery;
use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Infrastructure\ApiPlatform\Resource\BookResource;
//use Symfony\Component\Uid\AbstractUid;
use App\Shared\Application\Query\QueryBusInterface;

/**
 * @implements ProviderInterface<BookResource>
 */
final class BookItemProvider implements ProviderInterface
{
    public function __construct(
        private readonly QueryBusInterface $queryBus,
    ) {
    }

    /**
     * @param array<string, mixed> $uriVariables
     * @param array<string, mixed> $context
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): ?BookResource
    {
        /** @var Book|null $book */
        $book = $this->queryBus->ask(new FindBookQuery(BookId::fromString($uriVariables['id'])));

        return $book ? BookResource::fromModel($book) : null;
    }
}
