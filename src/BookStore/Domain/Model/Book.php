<?php

namespace App\BookStore\Domain\Model;

use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;
use DateTimeImmutable;
use DateTimeInterface;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class Book
{
    #[ORM\Id]
    #[ORM\Embedded(columnPrefix: false)]
    private BookId $id;

    #[ORM\Embedded(columnPrefix: false)]
    private BookName $name;

    #[ORM\Embedded(columnPrefix: false)]
    private Author $author;

    #[ORM\Embedded(columnPrefix: false)]
    private BookDescription $description;

    #[ORM\Embedded(columnPrefix: false)]
    private BookContent $content;

    #[ORM\Embedded(columnPrefix: false)]
    private Price $price;

    #[ORM\Column(type: 'datetime_immutable')]
    private DateTimeImmutable $createdAt;

    #[ORM\Column(type: 'datetime_immutable')]
    private DateTimeImmutable $updatedAt;

    public function __construct(
        BookName $name,
        Author $author,
        Price $price,
        ?BookDescription $description,
        ?BookContent $content,
    ) {
        $this->id = BookId::generate();
        $this->name = $name;
        $this->author = $author;
        $this->description = $description;
        $this->content = $content;
        $this->price = $price;
        $this->createdAt = new DateTimeImmutable();
        $this->updatedAt = new DateTimeImmutable();
    }

    public function update(
        ?BookName $name = null,
        ?BookDescription $description = null,
        ?Author $author = null,
        ?BookContent $content = null,
        ?Price $price = null,
    ): void {
        $this->name = $name ?? $this->name;
        $this->description = $description ?? $this->description;
        $this->author = $author ?? $this->author;
        $this->content = $content ?? $this->content;
        $this->price = $price ?? $this->price;
        $this->updatedAt = new DateTimeImmutable();
    }

    public function id(): BookId
    {
        return $this->id;
    }

    public function name(): BookName
    {
        return $this->name;
    }
    
    public function author(): Author
    {
        return $this->author;
    }

    public function description(): BookDescription
    {
        return $this->description;
    }

    public function content(): BookContent
    {
        return $this->content;
    }

    public function price(): Price
    {
        return $this->price;
    }

    public function createdAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    /**
     * Converts the book entity to an array for API responses
     */
    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'author' => $this->author,
            'price' => $this->price,
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt->format('Y-m-d H:i:s')
        ];
    }
}
