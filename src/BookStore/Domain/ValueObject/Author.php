<?php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use App\Shared\Utils\Assert;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final class Author
{
    #[ORM\Column(name: 'author', length: 255)]
    public readonly string $value;

    public function __construct(string $value)
    {
        Assert::stringNotEmpty(trim($value), "L'auteur ne peut pas être vide");
        Assert::LengthBetween($value, 1, 255);

        $this->value = $value;
    }

    public function isEqualTo(self $author): bool
    {
        return $author->value === $this->value;
    }
}
