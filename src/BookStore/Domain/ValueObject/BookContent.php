<?php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use App\Shared\Utils\Assert;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final class BookContent
{
    #[ORM\Column(name: 'content', type: 'text')]
    public readonly string $value;

    public function __construct(string $value)
    {
        Assert::lengthBetween($value, 1, 65535);
        $this->value = $value;
    }
}
