<?php

declare(strict_types=1);

namespace App\BookStore\Domain\ValueObject;

use App\Shared\Utils\Assert;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
final class BookDescription
{
    #[ORM\Column(name: 'description', length: 255)]
    public readonly string $value;

    public function __construct(string $value)
    {
        Assert::LengthBetween($value, 1, 255);

        $this->value = $value;
    }
}
