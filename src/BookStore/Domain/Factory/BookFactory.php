<?php

declare(strict_types=1);

namespace App\BookStore\Domain\Factory;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;

class BookFactory
{
    public function create(
        BookName $name,
        Author $author,
        Price $price,
        ?BookDescription $description,
        ?BookContent $content
    ): Book {
        return new Book(
            $name,
            $author,
            $price,
            $description,
            $content
        );
    }
}
