<?php

declare(strict_types=1);

namespace App\BookStore\Application\Command;

use App\BookStore\Domain\Factory\BookFactory;
use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\Shared\Application\Command\AsCommandHandler;

#[AsCommandHandler]
class CreateBookCommandHandler
{
    public function __construct(
        private readonly BookRepositoryInterface $bookRepository,
        private readonly BookFactory $bookFactory
    ) {
    }
    //factory new Book
    public function __invoke(CreateBookCommand $command): Book
    {
        $book = $this->bookFactory->create(
            $command->name,
            $command->author,
            $command->price,
            $command->description,
            $command->content
        );

        $this->bookRepository->add($book);

        return $book;
    }
}
