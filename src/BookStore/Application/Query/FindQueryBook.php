<?php

declare(strict_types=1);

namespace App\BookStore\Application\Query;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Domain\ValueObject\BookId;
use App\Shared\Application\Query\QueryInterface;
use Symfony\Component\Uid\Uuid;

final class FindQueryBook implements QueryInterface
{
    public function __construct(
        private readonly Uuid $bookId
    ) {
    }

    public function bookId(): Uuid
    {
        return $this->bookId;
    }
}
