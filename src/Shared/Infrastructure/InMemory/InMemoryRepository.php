<?php

declare(strict_types=1);

namespace App\Shared\Infrastructure\InMemory;

use App\Shared\Domain\Repository\RepositoryInterface;

/**
 * @template T of object
 *
 * @implements RepositoryInterface<T>
 */
abstract class InMemoryRepository implements RepositoryInterface
{
    /**
     * @var array<string, T>
     */
    protected array $entities = [];

    protected ?int $page = null;
    protected ?int $itemsPerPage = null;

    public function getIterator(): \Iterator
    {
        $offset = null !== $this->page ? ($this->page - 1) * $this->itemsPerPage : 0;

        yield from \array_slice($this->entities, $offset, $this->itemsPerPage);
    }

    public function count(): int
    {
        return \count($this->entities);
    }
}
