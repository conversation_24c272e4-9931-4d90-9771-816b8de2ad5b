<?php

declare(strict_types=1);

namespace App\Shared\Utils;

use InvalidArgumentException;

class Assert
{
    /**
     * Vérifie qu'une valeur est une chaîne
     */
    public static function string(mixed $value, string $message = 'La valeur doit être une chaîne'): void
    {
        if (!is_string($value)) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une valeur est un entier
     */
    public static function integer(mixed $value, string $message = 'La valeur doit être un entier'): void
    {
        if (!is_int($value)) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une valeur est un tableau
     */
    public static function array(mixed $value, string $message = 'La valeur doit être un tableau'): void
    {
        if (!is_array($value)) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une valeur n'est pas vide
     */
    public static function notEmpty(mixed $value, string $message = 'La valeur ne peut pas être vide'): void
    {
        if (empty($value)) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une valeur n'est pas null
     */
    public static function notNull(mixed $value, string $message = 'La valeur ne peut pas être null'): void
    {
        if ($value === null) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une clé existe dans un tableau
     */
    /**
     * @param array<mixed> $array
     */
    public static function keyExists(array $array, string $key, string $message = null): void
    {
        if (!array_key_exists($key, $array)) {
            $message = $message ?? "La clé '$key' est requise";
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie que toutes les clés existent dans un tableau
     */
    /**
     * @param array<mixed> $array
     * @param array<mixed> $keys
     */
    public static function keysExist(array $array, array $keys, string $message = null): void
    {
        foreach ($keys as $key) {
            if (!array_key_exists($key, $array)) {
                $message = $message ?? "La clé '$key' est requise";
                throw new InvalidArgumentException($message);
            }
        }
    }

    /**
     * Vérifie qu'une valeur est dans une liste de valeurs autorisées
     */
    /**
     * @param array<mixed> $choices
     */
    public static function oneOf(mixed $value, array $choices, string $message = null): void
    {
        if (!in_array($value, $choices, true)) {
            $choicesStr = implode(', ', array_map('json_encode', $choices));
            $message = $message ?? "La valeur doit être une de: $choicesStr";
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une chaîne n'est pas vide (après trim)
     */
    public static function stringNotEmpty(mixed $value, string $message = 'La chaîne ne peut pas être vide'): void
    {
        self::string($value);
        if (trim($value) === '') {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'un nombre est positif
     */
    public static function positive(mixed $value, string $message = 'La valeur doit être positive'): void
    {
        if (!is_numeric($value) || $value <= 0) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'un nombre est dans une plage
     */
    public static function range(mixed $value, int|float $min, int|float $max, string $message = null): void
    {
        if (!is_numeric($value) || $value < $min || $value > $max) {
            $message = $message ?? "La valeur doit être entre $min et $max";
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une chaîne correspond à un pattern regex
     */
    public static function regex(mixed $value, string $pattern, string $message = 'La valeur ne correspond pas au format attendu'): void
    {
        self::string($value);
        if (!preg_match($pattern, $value)) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une valeur est une instance d'une classe
     */
    public static function instanceOf(mixed $value, string $class, string $message = null): void
    {
        if (!($value instanceof $class)) {
            $message = $message ?? "La valeur doit être une instance de $class";
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'un tableau n'est pas vide
     */
    public static function notEmptyArray(mixed $value, string $message = 'Le tableau ne peut pas être vide'): void
    {
        self::array($value);
        if (empty($value)) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une valeur est un email valide
     */
    public static function email(mixed $value, string $message = 'Adresse email invalide'): void
    {
        self::string($value);
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException($message);
        }
    }

    /**
     * Vérifie qu'une valeur est une URL valide
     */
    public static function url(mixed $value, string $message = 'URL invalide'): void
    {
        self::string($value);
        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException($message);
        }
    }

    public static function lengthBetween(string $value, int $min, int $max): void
    {
        self::string($value);
        if (strlen($value) < $min || strlen($value) > $max) {
            throw new InvalidArgumentException("La longueur doit être entre $min et $max caractères");
        }
    }

    public static function greaterThanEq(int $value, int $min): void
    {
        if ($value < $min) {
            throw new InvalidArgumentException("La valeur doit être supérieure ou égale à $min");
        }
    }
}
