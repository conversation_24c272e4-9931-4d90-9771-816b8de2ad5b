const { pool } = require('../config/database');

class User {
  // Créer la table users si elle n'existe pas
  static async createTable() {
    const query = `
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        age INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    try {
      await pool.execute(query);
      console.log('✅ Table users créée/vérifiée');
    } catch (error) {
      console.error('❌ Erreur création table:', error.message);
    }
  }

  // Récupérer tous les utilisateurs
  static async getAll() {
    const [rows] = await pool.execute('SELECT * FROM users ORDER BY created_at DESC');
    return rows;
  }

  // Récupérer un utilisateur par ID
  static async getById(id) {
    const [rows] = await pool.execute('SELECT * FROM users WHERE id = ?', [id]);
    return rows[0];
  }

  // Créer un nouvel utilisateur
  static async create(userData) {
    const { nom, email, age } = userData;
    const [result] = await pool.execute(
      'INSERT INTO users (nom, email, age) VALUES (?, ?, ?)',
      [nom, email, age]
    );
    return result.insertId;
  }

  // Mettre à jour un utilisateur
  static async update(id, userData) {
    const { nom, email, age } = userData;
    const [result] = await pool.execute(
      'UPDATE users SET nom = ?, email = ?, age = ? WHERE id = ?',
      [nom, email, age, id]
    );
    return result.affectedRows > 0;
  }

  // Supprimer un utilisateur
  static async delete(id) {
    const [result] = await pool.execute('DELETE FROM users WHERE id = ?', [id]);
    return result.affectedRows > 0;
  }
}

module.exports = User;