Tests unitaires essentiels
Entités/Value Objects :

Validation des règles métier dans les entités
Comportement des méthodes métier
Invariants du domaine

Services de domaine :

Logique métier complexe
Interactions entre entités
Règles de validation spécifiques

Repositories (interfaces) :

Contrats de persistance via des mocks
Requêtes personnalisées

Command/Query Handlers :

Traitement des commandes CQRS
Validation des données d'entrée
Effets de bord (événements)

Serialization/Deserialization :

Transformation des données API
Validation des contraintes

Tests fonctionnels indispensables
Scénarios métier complets :

Flux utilisateur de bout en bout
Gestion des erreurs métier
Validation des permissions

Cas d'erreur :

Données invalides (400)
Ressources inexistantes (404)
Violations de contraintes métier