services:
    _instanceof:
            App\Shared\Application\Command\CommandInterface:
                tags:
                    - { name: messenger.message_handler, bus: command.bus }
            App\Shared\Application\Query\QueryInterface:
                tags:
                    - { name: messenger.message_handler, bus: query.bus }
    _defaults:
        autowire: true
        autoconfigure: true

    # Repositories
    App\BookStore\Domain\Repository\BookRepositoryInterface:
        class: App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository
        public: true

    App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository:
        public: true

    # Alias pour faciliter l'accès dans les tests
    test.book_repository:
        alias: App\BookStore\Domain\Repository\BookRepositoryInterface
        public: true

    # Make CommandBusInterface public for tests
    App\Shared\Application\Command\CommandBusInterface:
        alias: App\Shared\Infrastructure\Symfony\Messenger\MessengerCommandBus
        public: true

    # Make QueryBusInterface public for tests
    App\Shared\Application\Query\QueryBusInterface:
        alias: App\Shared\Infrastructure\Symfony\Messenger\MessengerQueryBus
        public: true
