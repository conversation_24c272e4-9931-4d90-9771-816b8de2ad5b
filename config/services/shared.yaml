services:
    _defaults:
        autowire: true
        autoconfigure: true

    # Auto-register all services in the Shared namespace
    App\Shared\:
        resource: '../../src/Shared'
        exclude:
            - '../../src/Shared/Infrastructure/Symfony/Kernel.php'

    # Configuration des bus de commandes et de requêtes
    App\Shared\Infrastructure\Symfony\Messenger\MessengerCommandBus:
        arguments:
            - '@command.bus'

    App\Shared\Infrastructure\Symfony\Messenger\MessengerQueryBus:
        arguments:
            - '@query.bus'

    # Alias pour les interfaces
    App\Shared\Application\Command\CommandBusInterface:
        alias: App\Shared\Infrastructure\Symfony\Messenger\MessengerCommandBus

    App\Shared\Application\Query\QueryBusInterface:
        alias: App\Shared\Infrastructure\Symfony\Messenger\MessengerQueryBus
