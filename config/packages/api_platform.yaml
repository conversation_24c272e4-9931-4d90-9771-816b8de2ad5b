api_platform:
    mapping:
        paths:
            - '%kernel.project_dir%/src/BookStore/Infrastructure/ApiPlatform/Resource'
    title: Hello API Platform
    version: 1.0.0
    formats:
        jsonld: ['application/ld+json']
    docs_formats:
        jsonld: ['application/ld+json']
        jsonopenapi: ['application/vnd.openapi+json']
        html: ['text/html']
    defaults:
        stateless: true
        cache_headers:
            vary: ['Content-Type', 'Authorization', 'Origin']
        extra_properties:
            standard_put: true
            rfc_7807_compliant_errors: true
    keep_legacy_inflector: false
    use_symfony_listeners: true
