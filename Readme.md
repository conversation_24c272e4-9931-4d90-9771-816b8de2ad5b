Flux de données

Controller reçoit la requête HTTP
Controller crée une Command/Query
Controller appelle le Use Case
Use Case utilise les services Domain et repositories
Repository (infrastructure) persiste les données
Controller retourne la réponse HTTP

Cette organisation sépare clairement les responsabilités et permet une grande flexibilité d'évolution !


Quand utiliser quoi ?
Utilisez DTO pour :

✅ Réponses d'API standardisées
✅ Validation des entrées
✅ Transformation de données
✅ Sérialisation/désérialisation

Utilisez Manager pour :
le manager s'occupe du crud 
✅ Opérations métier complexes
✅ Coordination de plusieurs services
✅ Transactions multi-étapes
✅ Logique d'orchestration

Alternative aux Managers : Les Use Cases qui ont le même rôle mais sont plus spécialisés (un Use Case = une action précise).


lors d'un test Unit je compare deux donnée entre elle celle de la fonction qui va etre crée createBook()sont resultat et de ceux que cela faire 


Request entrante (POST /books) via API Platform.

API Platform hydrate un CreateBookDto.

Le CreateBookProcessor (dans UI) reçoit ce DTO.

Il appelle un handler (dans Application) : CreateBookHandler.

Ce handler utilise un service du domaine (BookFactory, BookRepositoryInterface) pour appliquer les règles métier.

Le domaine crée un objet Book, lève des exceptions métiers si besoin.

Le repository Doctrine (dans Infrastructure) enregistre l’objet.

Une réponse est con$bookRepositorystruite avec un DTO + Transformer.

🧠 Pourquoi cette architecture est intéressante ?
Séparation des responsabilités stricte : domain, application, infrastructure, UI sont indépendants.

Facile à tester : le domaine peut être testé sans Symfony.

Scalable : on peut changer d’interface (CLI, API, etc) sans toucher au cœur métier.

Compatible avec API Platform tout en gardant le contrôle sur les couches

livebox 1G forfait 2990 par mois

pack special 

indepndanct 

box telephone appel 15.99 sans engagement 

CQRS + Bus (CommandBus / QueryBus)

Tu gardes toujours :

Entity

Repository

Controller

Mais tu ajoutes :

Command = un objet qui décrit ce qu’on veut faire (ex: CreateUserCommand)

CommandHandler = qui va exécuter la logique (ex: vérifier, valider, enregistrer)

CommandBus = envoie la commande au bon handler

Pareil pour les Query / QueryHandler / QueryBus

 Query + un QueryHandler.

 Le controller ne connaît pas la logique métier
 = $this->queryBus->dispatch(new GetActiveUsersQuery());

https://medium.com/beyn-technology/cqrs-principle-with-symfony-messenger-6c0fb2c28917


symfony model mvc hexagonal

 3. DDD (Domain-Driven Design)
Couplé souvent avec : CQRS + Event Sourcing
Objectif : Structurer l’app autour du domaine métier et du langage omniprésent

Éléments clés :

Agrégats

Entités

Value Objects

Domain Events

Bounded Contexts

✅ Aligné sur le métier, utile pour projets complexes
❌ Investissement initial importan

 6. Microservices Architecture
Symfony : utilisé comme un microservice isolé dans un écosystème (souvent via API REST ou Messenger avec RabbitMQ/Redis)

✅ Scalabilité, indépendance des équipes
❌ Complexité inter-services, orchestration, monitoring

La conception pilotée par le domaine (DDD) est une technologie de développement d'applications logicielles qui allie la technique et le domaine. L'objectif est d'implémenter des règles métier et des workflows complexes dans des logiciels basés sur des comportements et des concepts concrets.

Les éléments clés du DDD comprennent :

Entités : Objets dotés d’une identité distincte qui persiste dans le temps.
Objets de valeur : objets immuables qui décrivent des aspects du domaine.
Agrégats : groupes d’entités et d’objets de valeur traités comme une seule unité.
Référentiels : abstractions pour récupérer et stocker des agrégats.
Événements du domaine : événements qui reflètent quelque chose d’important qui s’est produit dans le domaine.
Contextes délimités : limites explicites dans lesquelles un modèle de domaine particulier est défini et applicable.

La méthode __invoke(CreateBookCommand $command): Book du CreateBookCommandHandler est appelée automatiquement par le bus de commandes (CommandBus) lorsqu'une commande de type CreateBookCommand est envoyée.

D'après les extraits de code fournis, voici comment cela fonctionne:

Le MessengerCommandBus (implémentation de CommandBusInterface) reçoit la commande via sa méthode dispatch()
Le bus utilise Symfony Messenger (via HandleTrait) pour router la commande au bon handler
Grâce à l'attribut #[AsCommandHandler] sur la classe, le système sait que cette classe doit traiter les commandes CreateBookCommand
La méthode __invoke() est appelée automatiquement (c'est une méthode magique en PHP qui permet d'utiliser un objet comme une fonction)

Cette annotation indique que la classe CreateBookProcessor implémente l'interface ProcessorInterface avec un type générique spécifique BookResource.

Elle précise que ce processeur travaille spécifiquement avec des objets de type BookResource
Elle aide les outils d'analyse statique (comme PHPStan) à comprendre que la méthode process() retournera un objet BookResource
Elle améliore l'autocomplétion dans les IDE comme PHPStorm


En termes simples, un agrégat (aggregate) est un concept du Domain-Driven Design (DDD). Il s'agit d'un regroupement d'objets (entités et objets de valeur) qui sont liés logiquement et qui doivent être traités comme une seule unité.

L'objectif principal d'un agrégat est de garantir la cohérence des données. Toute modification des objets à l'intérieur de l'agrégat doit passer par une seule porte d'entrée : l'aggrégat racine (aggregate root).

Pour illustrer cela avec ce projet, l'entité User est un excellent exemple d'aggrégat racine. Un User peut avoir un email, un mot de passe, des LoginHistory, etc. Au lieu de manipuler ces objets séparément, vous passez par l'objet User pour, par exemple, mettre à jour l'email ou vérifier un mot de passe. Cela permet à l'entité User de s'assurer que toutes les règles métier (par exemple, "un email doit être unique") sont respectées avant que la modification ne soit enregistrée.


C'est le point d'entrée pour les modifications : Pour changer l'email, le mot de passe ou pour verrouiller le compte, vous n'accédez pas directement à ces propriétés. Vous devez utiliser les méthodes de l'objet User, comme updateEmail(), resetPassword() ou lockAccount().

Il garantit la cohérence : L'agrégat s'assure que les règles métier sont toujours respectées. Par exemple, la méthode updatePassword() vérifie d'abord si le mot de passe actuel est valide avant d'autoriser la modification. C'est une règle de cohérence que l'agrégat User est chargé de maintenir.

Il "possède" d'autres objets : Bien qu'ils ne soient pas directement dans ce fichier, des objets comme LoginHistory ou VerificationToken sont logiquement liés à un User. Dans la philosophie DDD, on ne modifierait pas un LoginHistory directement. On passerait par l'agrégat User pour ajouter une nouvelle entrée à l'historique de connexion, par exemple. Le User est le "gardien" de toutes les données qui le concernent.

Il a une identité unique : L'agrégat a un identifiant unique, ici UserId, qui permet de le retrouver et de s'assurer qu'on travaille sur le bon "paquet" de données.

En résumé, voyez l'agrégat User comme un "gardien" pour toutes les données et les règles qui concernent un utilisateur. C'est une barrière de protection qui empêche de mettre le système dans un état incohérent (par exemple, changer un mot de passe sans vérifier l'ancien).

