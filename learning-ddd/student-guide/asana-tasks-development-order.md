# 📋 Tâches Asana - Ordre de Développement DDD

## 🎯 Vue d'Ensemble

Ce document liste les tâches Asana dans l'ordre logique de développement pour implémenter une architecture DDD complète avec API Platform, en suivant le flux décrit dans `flow-explanation.md`.


api book
Get 
New, Update, Delete // COMMAND 

/books
/books/{id}


## 🏗️ Phase 1 : Fondations du Domaine

### 1.1 Value Objects (Cœur du Domaine)
- [ ] **Créer BookId Value Object**
  - Génération UUID automatique
  - Validation format UUID
  - Tests unitaires complets

- [ ] **Créer BookName Value Object**
  - Validation longueur (1-255 caractères)
  - Validation caractères autorisés
  - Tests edge cases

- [ ] **Créer BookDescription Value Object**
  - Validation longueur (1-1023 caractères)
  - Gestion valeurs nulles optionnelles
  - Tests de validation

- [ ] **Créer Author Value Object**
  - Validation nom auteur (1-255 caractères)
  - Normalisation format
  - Tests unitaires

- [ ] **Créer BookContent Value Object**
  - Validation contenu non vide
  - Gestion texte long
  - Tests de performance

- [ ] **Créer Price Value Object**
  - Validation prix positif ou zéro
  - Gestion centimes (int)
  - Méthode applyDiscount()
  - Tests calculs

### 1.2 Entité Domaine
- [ ] **Créer Book Entity**
  - Constructeur avec Value Objects
  - Méthode update() avec règles métier
  - Méthode applyDiscount()
  - Validation invariants (ex: livre emprunté)
  - Getters pour Value Objects
  - Tests unitaires règles métier

## 🏗️ Phase 2 : Couche Application

### 2.1 Commands et Interfaces
- [ ] **Créer CommandInterface générique**
  - Interface avec générics PHP
  - Documentation type de retour

- [ ] **Créer CreateBookCommand**
  - Propriétés readonly avec Value Objects
  - Implémentation CommandInterface<Book>
  - Tests de construction

- [ ] **Créer CommandBusInterface**
  - Méthode dispatch() générique
  - Gestion exceptions métier
  - Documentation interface

### 2.2 Command Handlers
- [ ] **Créer CreateBookCommandHandler**
  - Attribut #[AsCommandHandler]
  - Injection BookRepositoryInterface
  - Logique création Book
  - Appel repository->add()
  - Tests unitaires avec mock repository

### 2.3 Repository Interface
- [ ] **Créer BookRepositoryInterface**
  - Méthodes CRUD de base (add, remove, ofId)
  - Méthodes de recherche métier (withAuthor, withCheapestsFirst)
  - Fluent interface pour filtres
  - Documentation comportement

## 🏗️ Phase 3 : Infrastructure

### 3.1 Command Bus Implementation
- [ ] **Créer MessengerCommandBus**
  - Implémentation CommandBusInterface
  - Utilisation Symfony Messenger
  - Gestion HandlerFailedException
  - Unwrapping exceptions métier
  - Configuration middleware transactions

### 3.2 Repository Doctrine
- [ ] **Créer DoctrineBookRepository**
  - Implémentation BookRepositoryInterface
  - Injection EntityManagerInterface
  - Méthodes CRUD avec Doctrine
  - QueryBuilder pour recherches
  - Fluent interface avec clone
  - Tests d'intégration

### 3.3 Configuration Symfony
- [ ] **Configuration Messenger**
  - Bus command.bus avec middleware
  - Middleware doctrine_transaction
  - Auto-configuration handlers
  - Tests configuration

## 🏗️ Phase 4 : API Platform

### 4.1 Resource API
- [ ] **Créer BookResource**
  - Attribut #[ApiResource]
  - Configuration operations (Post, Get, etc.)
  - Annotations validation Symfony
  - Groupes de validation (create, update)
  - Méthode fromModel() statique

### 4.2 Processors
- [ ] **Créer CreateBookProcessor**
  - Implémentation ProcessorInterface
  - Injection CommandBusInterface
  - Validation stricte données entrée
  - Transformation Resource → Command
  - Dispatch Command Bus
  - Transformation Entity → Resource retour
  - Tests unitaires complets

## 🏗️ Phase 5 : Tests et Validation

### 5.1 Tests Unitaires
- [ ] **Tests Value Objects**
  - Validation constructeurs
  - Tests edge cases
  - Tests exceptions

- [ ] **Tests Entity Book**
  - Tests règles métier
  - Tests invariants
  - Tests méthodes update/discount

- [ ] **Tests Command Handler**
  - Tests avec mock repository
  - Tests création entité
  - Tests appel repository

### 5.2 Tests d'Intégration
- [ ] **Tests Repository Doctrine**
  - Tests CRUD complets
  - Tests requêtes métier
  - Tests fluent interface
  - Tests performance

- [ ] **Tests Processor**
  - Tests transformation complète
  - Tests validation
  - Tests gestion erreurs

### 5.3 Tests End-to-End
- [ ] **Tests API Platform**
  - Tests endpoints complets
  - Tests validation HTTP
  - Tests réponses JSON
  - Tests codes status

## 🏗️ Phase 6 : Extensions et Optimisations

### 6.1 Fonctionnalités Avancées
- [ ] **Ajouter UpdateBookCommand/Handler**
  - Command avec champs optionnels
  - Handler avec logique update
  - Processor correspondant

- [ ] **Ajouter DiscountBookCommand/Handler**
  - Command avec Discount Value Object
  - Handler avec règles métier discount
  - Tests règles complexes

### 6.2 Middleware et Monitoring
- [ ] **Ajouter middleware audit log**
  - Log des commandes exécutées
  - Traçabilité actions utilisateur

- [ ] **Ajouter middleware validation**
  - Validation automatique commands
  - Centralisation règles validation

### 6.3 Performance et Scalabilité
- [ ] **Optimiser requêtes Doctrine**
  - Eager loading relations
  - Index base de données
  - Cache requêtes

- [ ] **Ajouter cache Repository**
  - Cache résultats recherches
  - Invalidation cache intelligente

## 📊 Ordre de Priorité Recommandé

### 🥇 **Priorité 1 (Critique)**
1. Value Objects (BookId, BookName, Price)
2. Book Entity avec règles métier
3. CreateBookCommand/Handler
4. BookRepositoryInterface

### 🥈 **Priorité 2 (Important)**
5. DoctrineBookRepository
6. MessengerCommandBus
7. BookResource API Platform
8. CreateBookProcessor

### 🥉 **Priorité 3 (Utile)**
9. Tests unitaires complets
10. Tests d'intégration
11. Configuration Symfony
12. Tests end-to-end

### 🏅 **Priorité 4 (Extensions)**
13. Commands additionnels (Update, Discount)
14. Middleware avancés
15. Optimisations performance
16. Monitoring et logs

## 🎯 Critères de Validation par Phase

### ✅ **Phase 1 Terminée Quand :**
- Tous les Value Objects créés et testés
- Book Entity avec règles métier fonctionnelles
- Tests unitaires passent à 100%

### ✅ **Phase 2 Terminée Quand :**
- Command/Handler créés et testés
- Interface repository définie
- Logique application isolée

### ✅ **Phase 3 Terminée Quand :**
- Repository Doctrine fonctionnel
- Command Bus configuré
- Tests d'intégration passent

### ✅ **Phase 4 Terminée Quand :**
- API Platform opérationnelle
- Processor fonctionnel
- Tests end-to-end passent

### ✅ **Phase 5 Terminée Quand :**
- Couverture tests > 90%
- Tous les tests passent
- Documentation complète

---

**💡 Note :** Suivre cet ordre garantit une progression logique du cœur du domaine vers l'infrastructure, respectant les principes DDD et minimisant les dépendances circulaires.
