## 4. Gestion des Routes

Symfony offre plusieurs manières de configurer les routes.

### Avec les Attributs (Recommandé)

La méthode moderne consiste à utiliser les attributs `#[Route]` directement dans les contrôleurs.

```php
// src/Controller/BookController.php
namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class BookController extends AbstractController
{
    #[Route('/book/{id}', name: 'book_show', methods: ['GET'])]
    public function show(int $id): Response
    {
        // ...
    }
}
```

### Avec YAML

Vous pouvez également définir les routes dans `config/routes.yaml`.

```yaml
# config/routes.yaml
book_show:
    path: /book/{id}
    controller: App\Controller\BookController::show
    methods: [GET]
```

## 5. GitHub Actions (CI/CD)

Pour automatiser vos tests et déploiements, vous pouvez utiliser GitHub Actions.

### Exemple de workflow de base

Créez un fichier `.github/workflows/ci.yml` :

```yaml
name: CI

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'

      - name: Install dependencies
        run: composer install --prefer-dist --no-progress --no-suggest

      - name: Run tests
        run: ./bin/phpunit
```

Ce workflow vérifie le code, installe les dépendances et exécute les tests à chaque `push` ou `pull request`.

## 6. Injection de Dépendances et Services

Le conteneur de services de Symfony gère l'instanciation et l'injection des objets (services) de votre application.

### Configuration des services

La configuration se fait principalement dans `config/services.yaml`. Ces fichiers de configuration sont chargés depuis la méthode `configureContainer` dans `src/Shared/Infrastructure/Symfony/Kernel.php`. Par défaut, Symfony utilise l'autoconfiguration et l'autowiring, ce qui réduit la configuration manuelle.

*   **Autowiring** : Symfony injecte automatiquement les dépendances dans les constructeurs de vos services si elles sont typées.
*   **Autoconfiguration** : Applique automatiquement des tags à vos services en se basant sur les interfaces qu'ils implémentent.

### Exemple de service manuel

Si vous avez besoin de configurer un service manuellement :

```yaml
# config/services.yaml
services:
    _defaults:
        autowire: true
        autoconfigure: true

    App\Services\MyCustomService:
        arguments:
            $apiKey: '%env(SOME_API_KEY)%'
```

## 7. Bonnes pratiques

*   **Validation** : Validez les données entrantes avec le composant `Validator`.
*   **Sécurité** : Utilisez le composant `Security` pour gérer l'authentification et les autorisations.