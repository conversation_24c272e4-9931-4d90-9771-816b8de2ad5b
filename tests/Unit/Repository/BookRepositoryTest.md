<?php

// tests/Unit/Repository/BookRepositoryTest.php
namespace App\Tests\Unit\Repository;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use App\BookStore\Infrastructure\Repository\BookRepository;
use App\BookStore\Domain\Model\Book;

class BookRepositoryTest extends TestCase
{
    private EntityManagerInterface|MockObject $entityManager;
    private ClassMetadata|MockObject $classMetadata;
    private BookRepository $repository;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->classMetadata = $this->createMock(ClassMetadata::class);
        $this->classMetadata->name = Book::class;

        $this->repository = new BookRepository($this->entityManager);
    }

    public function testFindByAuthor(): void
    {
        $author = 'Hugo';
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(\Doctrine\ORM\AbstractQuery::class);
        $books = [new Book(), new Book()];

        $this->entityManager
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $queryBuilder
            ->expects($this->once())
            ->method('select')
            ->with('b')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('from')
            ->with(Book::class, 'b')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('where')
            ->with('LOWER(b.author) LIKE :author')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('setParameter')
            ->with('author', '%' . strtolower($author) . '%')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        $query
            ->expects($this->once())
            ->method('getResult')
            ->willReturn($books);

        $result = $this->repository->findByAuthor($author);

        $this->assertSame($books, $result);
    }

    public function testFindByPublishedAfter(): void
    {
        $date = new \DateTime('2022-01-01');
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(\Doctrine\ORM\AbstractQuery::class);
        $books = [new Book()];

        $this->entityManager
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $queryBuilder
            ->expects($this->once())
            ->method('select')
            ->with('b')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('from')
            ->with(Book::class, 'b')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('where')
            ->with('b.publishedAt >= :date')
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('setParameter')
            ->with('date', $date)
            ->willReturnSelf();

        $queryBuilder
            ->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        $query
            ->expects($this->once())
            ->method('getResult')
            ->willReturn($books);

        $result = $this->repository->findByPublishedAfter($date);

        $this->assertSame($books, $result);
    }
}
