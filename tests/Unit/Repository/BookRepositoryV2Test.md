<?php

namespace App\Tests\Unit\Repository;

use App\BookStore\Domain\Model\Book;
//use App\Tests\Factory\BookFactory;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookId;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;
use App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Uid\Uuid;

class BookRepositoryV2Test extends TestCase
{
    private DoctrineBookRepository $repository;
    private EntityManagerInterface|MockObject $entityManager;
    private QueryBuilder|MockObject $queryBuilder;

    public function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->queryBuilder = $this->createMock(QueryBuilder::class);
        $this->repository = new DoctrineBookRepository($this->entityManager);

        // Configuration du QueryBuilder mock pour le constructeur
        $this->entityManager
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($this->queryBuilder);

        $this->queryBuilder
           ->expects($this->once())
           ->method('select')
           ->with('book')
           ->willReturnSelf();
        $this->queryBuilder
            ->expects($this->once())
            ->method('from')
            ->with(Book::class, 'book')
            ->willReturnSelf();
    }

    public function testAdd(): void
    {
        // Arrange
        $book = $this->createMock(Book::class);

        // Assert
        $this->entityManager
            ->expects($this->once())
            ->method('persist')
            ->with($book);

        // Act
        $this->repository->add($book);
    }
}
