<?php


// tests/Unit/Strategy/FilterByDateStrategyTest.php
namespace App\Tests\Unit\Strategy;

use App\Strategy\FilterByDateStrategy;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class FilterByDateStrategyTest extends TestCase
{
    private FilterByDateStrategy $strategy;
    private QueryBuilder|MockObject $queryBuilder;

    protected function setUp(): void
    {
        $this->strategy = new FilterByDateStrategy();
        $this->queryBuilder = $this->createMock(QueryBuilder::class);
    }

    public function testApplyWithValidDate(): void
    {
        $date = '2022-01-01';

        $this->queryBuilder
            ->expects($this->once())
            ->method('andWhere')
            ->with('b.publishedAt >= :publishedAfter')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('setParameter')
            ->with('publishedAfter', new \DateTime($date))
            ->willReturnSelf();

        $result = $this->strategy->apply($this->queryBuilder, $date);

        $this->assertSame($this->queryBuilder, $result);
    }

    public function testApplyWithInvalidDate(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Format de date invalide');

        $this->strategy->apply($this->queryBuilder, 'invalid-date');
    }

    public function testApplyWithEmptyDate(): void
    {
        $this->queryBuilder
            ->expects($this->never())
            ->method('andWhere');

        $this->queryBuilder
            ->expects($this->never())
            ->method('setParameter');

        $result = $this->strategy->apply($this->queryBuilder, '');

        $this->assertSame($this->queryBuilder, $result);
    }

    public function testApplyWithNullDate(): void
    {
        $this->queryBuilder
            ->expects($this->never())
            ->method('andWhere');

        $this->queryBuilder
            ->expects($this->never())
            ->method('setParameter');

        $result = $this->strategy->apply($this->queryBuilder, null);

        $this->assertSame($this->queryBuilder, $result);
    }
}
