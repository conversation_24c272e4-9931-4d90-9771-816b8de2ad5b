<?php

// tests/Unit/Strategy/FilterByAuthorStrategyTest.php
namespace App\Tests\Unit\Strategy;

use App\Strategy\FilterByAuthorStrategy;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class FilterByAuthorStrategyTest extends TestCase
{
    private FilterByAuthorStrategy $strategy;
    private QueryBuilder|MockObject $queryBuilder;

    protected function setUp(): void
    {
        $this->strategy = new FilterByAuthorStrategy();
        $this->queryBuilder = $this->createMock(QueryBuilder::class);
    }

    public function testApplyWithAuthorFilter(): void
    {
        $authorName = 'Hugo';

        $this->queryBuilder
            ->expects($this->once())
            ->method('andWhere')
            ->with('LOWER(b.author) LIKE :author')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('setParameter')
            ->with('author', '%' . strtolower($authorName) . '%')
            ->willReturnSelf();

        $result = $this->strategy->apply($this->queryBuilder, $authorName);

        $this->assertSame($this->queryBuilder, $result);
    }

    public function testApplyWithEmptyAuthor(): void
    {
        $this->queryBuilder
            ->expects($this->never())
            ->method('andWhere');

        $this->queryBuilder
            ->expects($this->never())
            ->method('setParameter');

        $result = $this->strategy->apply($this->queryBuilder, '');

        $this->assertSame($this->queryBuilder, $result);
    }

    public function testApplyWithNullAuthor(): void
    {
        $this->queryBuilder
            ->expects($this->never())
            ->method('andWhere');

        $this->queryBuilder
            ->expects($this->never())
            ->method('setParameter');

        $result = $this->strategy->apply($this->queryBuilder, null);

        $this->assertSame($this->queryBuilder, $result);
    }
}
