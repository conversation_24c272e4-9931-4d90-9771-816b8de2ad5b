<?php

// tests/Unit/Service/BookManagerTest.php
namespace App\Tests\Unit\Service;

use App\Dto\BookInputDto;
use App\Entity\Book;
use App\Factory\BookFactory;
use App\Repository\BookRepository;
use App\Service\BookManager;
use App\Strategy\FilterStrategyInterface;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class BookManagerTest extends TestCase
{
    private BookRepository|MockObject $repository;
    private EntityManagerInterface|MockObject $entityManager;
    private BookFactory|MockObject $bookFactory;
    private BookManager $bookManager;

    protected function setUp(): void
    {
        $this->repository = $this->createMock(BookRepository::class);
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->bookFactory = $this->createMock(BookFactory::class);

        $this->bookManager = new BookManager(
            $this->repository,
            $this->entityManager,
            $this->bookFactory
        );
    }

    public function testCreateBookSuccess(): void
    {
        $dto = new BookInputDto();
        $dto->title = 'Test Book';
        $dto->author = 'Test Author';

        $book = new Book();
        $book->setTitle('Test Book');
        $book->setAuthor('Test Author');

        $this->bookFactory
            ->expects($this->once())
            ->method('createFromDto')
            ->with($dto)
            ->willReturn($book);

        $this->entityManager
            ->expects($this->once())
            ->method('persist')
            ->with($book);

        $this->entityManager
            ->expects($this->once())
            ->method('flush');

        $result = $this->bookManager->createBook($dto);

        $this->assertSame($book, $result);
    }

    public function testCreateBookWithDuplicateIsbn(): void
    {
        $dto = new BookInputDto();
        $dto->title = 'Test Book';
        $dto->author = 'Test Author';
        $dto->isbn = '978-2-07-040857-4';

        $existingBook = new Book();
        $this->repository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['isbn' => '978-2-07-040857-4'])
            ->willReturn($existingBook);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Un livre avec cet ISBN existe déjà');

        $this->bookManager->createBook($dto);
    }

    public function testGetAllBooks(): void
    {
        $books = [
            $this->createBookMock('Book 1', 'Author 1'),
            $this->createBookMock('Book 2', 'Author 2'),
        ];

        $this->repository
            ->expects($this->once())
            ->method('findAll')
            ->willReturn($books);

        $result = $this->bookManager->getAllBooks();

        $this->assertSame($books, $result);
    }

    public function testGetFilteredBooks(): void
    {
        $filterStrategy = $this->createMock(FilterStrategyInterface::class);
        $queryBuilder = $this->createMock(\Doctrine\ORM\QueryBuilder::class);
        $books = [$this->createBookMock('Filtered Book', 'Filtered Author')];

        $this->repository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->with('b')
            ->willReturn($queryBuilder);

        $filterStrategy
            ->expects($this->once())
            ->method('apply')
            ->with($queryBuilder, 'test_value')
            ->willReturn($queryBuilder);

        $query = $this->createMock(\Doctrine\ORM\AbstractQuery::class);
        $queryBuilder
            ->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        $query
            ->expects($this->once())
            ->method('getResult')
            ->willReturn($books);

        $result = $this->bookManager->getFilteredBooks([$filterStrategy => 'test_value']);

        $this->assertSame($books, $result);
    }

    private function createBookMock(string $title, string $author): Book|MockObject
    {
        $book = $this->createMock(Book::class);
        $book->method('getTitle')->willReturn($title);
        $book->method('getAuthor')->willReturn($author);
        return $book;
    }
}
