<?php

// tests/Unit/Dto/BookInputDtoTest.php
namespace App\Tests\Unit\Dto;

use App\Dto\BookInputDto;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class BookInputDtoTest extends TestCase
{
    private ValidatorInterface $validator;

    protected function setUp(): void
    {
        $this->validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();
    }

    public function testValidDto(): void
    {
        $dto = new BookInputDto();
        $dto->title = 'Le Petit Prince';
        $dto->author = '<PERSON>-Exupé<PERSON>';
        $dto->isbn = '978-2-07-040857-4';
        $dto->publishedAt = new \DateTime('1943-04-06');
        $dto->pages = 96;

        $violations = $this->validator->validate($dto);
        $this->assertCount(0, $violations);
    }

    public function testMinimalValidDto(): void
    {
        $dto = new BookInputDto();
        $dto->title = '1984';
        $dto->author = 'George Orwell';

        $violations = $this->validator->validate($dto);
        $this->assertCount(0, $violations);
    }

    public function testInvalidDtoMissingTitle(): void
    {
        $dto = new BookInputDto();
        $dto->author = 'Victor <PERSON>';

        $violations = $this->validator->validate($dto);
        $this->assertCount(1, $violations);
        $this->assertEquals('title', $violations[0]->getPropertyPath());
    }

    public function testInvalidDtoMissingAuthor(): void
    {
        $dto = new BookInputDto();
        $dto->title = 'Notre-Dame de Paris';

        $violations = $this->validator->validate($dto);
        $this->assertCount(1, $violations);
        $this->assertEquals('author', $violations[0]->getPropertyPath());
    }

    public function testInvalidDtoTitleTooLong(): void
    {
        $dto = new BookInputDto();
        $dto->title = str_repeat('a', 256); // 256 characters
        $dto->author = 'Test Author';

        $violations = $this->validator->validate($dto);
        $this->assertCount(1, $violations);
        $this->assertEquals('title', $violations[0]->getPropertyPath());
    }

    public function testInvalidDtoNegativePages(): void
    {
        $dto = new BookInputDto();
        $dto->title = 'Test Book';
        $dto->author = 'Test Author';
        $dto->pages = -10;

        $violations = $this->validator->validate($dto);
        $this->assertCount(1, $violations);
        $this->assertEquals('pages', $violations[0]->getPropertyPath());
    }
}
