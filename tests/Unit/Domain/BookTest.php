<?php

declare(strict_types=1);

namespace Tests\Unit\Domain;

use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;
use App\Shared\Utils\Assert;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;

class BookTest extends TestCase
{
    public function test_can_create_book_with_required_fields(): void
    {
        // Création des Value Objects
        $name = new BookName('Clean Architecture');
        $author = new Author('Robert <PERSON> Martin');
        $description = new BookDescription('Guide pour une architecture logicielle propre');
        $content = new BookContent('Chapitre 1: Introduction...');
        $price = new Price(42);
        $book = new Book($name, $author, $price, $description, $content);
        $this->assertEquals($name, $book->name());
        $this->assertEquals($author, $book->author());
        //$this->assertNull($book->id());
    }
    
  
    
    public function test_cannot_create_book_with_empty_title(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Le titre ne peut pas être vide');
        
        $name = new BookName('');
        //$author = new Author('Robert C. Martin');
        //$description = new BookDescription('Guide pour une architecture logicielle propre');
        //$book = new Book($name, $author, $description);
    }
    
    public function test_cannot_create_book_with_empty_author(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('L\'auteur ne peut pas être vide');
        
        $author = new Author('');
        //$description = new BookDescription('Guide pour une architecture logicielle propre');
        //$book = new Book($name, $author, $description);
    }

    public function test_can_update_book_with_required_fields(): void
    {
        // Update des Value Objects
        $name = new BookName('Clean Architecture');
        $author = new Author('Robert C. Martin');
        $description = new BookDescription('Guide pour une architecture logicielle propre');
        $content = new BookContent('Chapitre 1: Introduction...');
        $price = new Price(42);
        $book = new Book($name, $author, $price, $description, $content);
        $book->update(null, null, null, null, new Price(50));
        $this->assertEquals(new Price(50), $book->price());
        $this->assertEquals($name, $book->name());
    }
}
