<?php

// tests/Unit/Domain/BookManagerTest.php
declare(strict_types=1);

namespace Tests\Unit\Domain;

use App\Application\DTO\CreateBookDto;
use App\Domain\{Book, BookManager, BookRepository};
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;

class BookManagerTest extends TestCase
{
    private BookRepository $bookRepository;
    private BookManager $bookManager;

    public function setUp(): void
    {
        $this->bookRepository = createMock(BookRepository::class);
        $this->bookManager = new BookManager($this->bookRepository);
    }

    //tdo new Book
    public function test_can_create_book_with_valid_data()
    {
        $publishedAt = new \DatetimeImmutable('1949-06-08');
        $dto = new CreateBookDto('1984', '<PERSON>');
        $expectedBook = new Book('1984', '<PERSON>');
        $expectedBook->assignId(1);
        
        //$this->bookRepository->save($book)
        $this->bookRepository
            ->expects($this->once)
            ->method('save')
            ->with($this->callback(function (Book $book) {
                return ($book->getTitle() === '1984' &&
                $book->getAuthor() === 'George Orwell');
            }))
            ->willReturn($expectedBook);

        
        $result = $this->bookManager->createBook($dto);
    }
}
