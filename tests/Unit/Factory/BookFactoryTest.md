<?php

// tests/Unit/Factory/BookFactoryTest.php
namespace App\Tests\Unit\Factory;

use App\Dto\BookInputDto;
use App\Entity\Book;
use App\Factory\BookFactory;
use PHPUnit\Framework\TestCase;

class BookFactoryTest extends TestCase
{
    private BookFactory $factory;

    protected function setUp(): void
    {
        $this->factory = new BookFactory();
    }

    public function testCreateFromDtoWithCompleteData(): void
    {
        $dto = new BookInputDto();
        $dto->title = 'Le Petit Prince';
        $dto->author = 'Antoine de Saint-Exupéry';
        $dto->isbn = '978-2-07-040857-4';
        $dto->publishedAt = new \DateTime('1943-04-06');
        $dto->pages = 96;

        $book = $this->factory->createFromDto($dto);

        $this->assertInstanceOf(Book::class, $book);
        $this->assertEquals('Le Petit Prince', $book->getTitle());
        $this->assertEquals('Antoine de Saint-Exupéry', $book->getAuthor());
        $this->assertEquals('978-2-07-040857-4', $book->getIsbn());
        $this->assertEquals(new \DateTime('1943-04-06'), $book->getPublishedAt());
        $this->assertEquals(96, $book->getPages());
    }

    public function testCreateFromDtoWithMinimalData(): void
    {
        $dto = new BookInputDto();
        $dto->title = '1984';
        $dto->author = 'George Orwell';

        $book = $this->factory->createFromDto($dto);

        $this->assertInstanceOf(Book::class, $book);
        $this->assertEquals('1984', $book->getTitle());
        $this->assertEquals('George Orwell', $book->getAuthor());
        $this->assertNull($book->getIsbn());
        $this->assertNull($book->getPublishedAt());
        $this->assertNull($book->getPages());
    }

    public function testCreateFromDtoSetsCreatedAt(): void
    {
        $dto = new BookInputDto();
        $dto->title = 'Test Book';
        $dto->author = 'Test Author';

        $before = new \DateTime();
        $book = $this->factory->createFromDto($dto);
        $after = new \DateTime();

        $this->assertInstanceOf(\DateTime::class, $book->getCreatedAt());
        $this->assertGreaterThanOrEqual($before, $book->getCreatedAt());
        $this->assertLessThanOrEqual($after, $book->getCreatedAt());
    }
}
