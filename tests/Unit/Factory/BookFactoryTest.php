<?php

declare(strict_types=1);

namespace App\Tests\Unit\Factory;

use App\BookStore\Domain\Factory\BookFactory;
use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\ValueObject\Author;
use App\BookStore\Domain\ValueObject\BookContent;
use App\BookStore\Domain\ValueObject\BookDescription;
use App\BookStore\Domain\ValueObject\BookName;
use App\BookStore\Domain\ValueObject\Price;
use PHPUnit\Framework\TestCase;

class BookFactoryTest extends TestCase
{
    public function testCreateBook(): void
    {
        $factory = new BookFactory();

        $name = new BookName('The Lord of the Rings');
        $author = new Author('J.R.R. Tolkien');
        $price = new Price(2550);
        $description = new BookDescription('A great book');
        $content = new BookContent('Long text...');

        $book = $factory->create($name, $author, $price, $description, $content);

        $this->assertInstanceOf(Book::class, $book);
        $this->assertSame($name, $book->name());
        $this->assertSame($author, $book->author());
        $this->assertSame($price, $book->price());
        $this->assertSame($description, $book->description());
        $this->assertSame($content, $book->content());
    }
}
