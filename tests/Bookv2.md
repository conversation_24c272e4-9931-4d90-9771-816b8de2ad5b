<?php

// src/App/Entity/Book.php
namespace App\Domain\Entity;

use ApiPlatform\Core\Annotation\ApiResource;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use InvalidArgumentException;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * A book.
 * ApiResource()
 */
//#[ORM\Entity(repositoryClass: BookRepository::class)]
#[ORM\Table(name: 'books')]
#[ORM\Index(columns: ['author'], name: 'idx_book_author')]
#[ORM\Index(columns: ['published_at'], name: 'idx_book_published_at')]
class Bookv2
{
    /**
     * @var int The id of this book.
     */
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank]
    private string $title;

    /**
     * @var string the author of this book.
     * @ORM\Column(type='string', length=255)
     * @Assert\NotBlank
     */
    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    private $author;

    /**
     * @var string the IS8N of this book
     * @ORM\Column(type='string', length=255, unique=true)
     * @Assert\NotBlank
     * @Assert\Isbn
     */
    #[ORM\Column(type: 'string', length: 255, unique: true)]
    #[Assert\NotBlank]
    #[Assert\Isbn]
    private ?string $isbn = null;

    /**
     * @var \DateTimeInterface the publication date of this book
     * @ORM\Column(type='datetime')
     * @Assert\Date
     * @Assert\LessThan("today")
     * @Assert\GreaterThan("-10 years")
     */
    #[ORM\Column(type: 'datetime')]
    #[Assert\Date]
    #[Assert\LessThan("today")]
    #[Assert\GreaterThan("-10 years")]
    private $publishedAt;

    /**
     * @var \DateTimeInterface the creation date of this book
     * @ORM\Column(type="datetime")
     * @Assert\Date
     */
    #[ORM\Column(type: 'datetime')]
    #[Assert\Date]
    private \DateTime $createdAt;


    /**
     * @var \DateTimeInterface the last update date of this book
     * @ORM\Column(type='datetime')
     * @Assert\Date
     */
    #[ORM\Column(type: 'datetime')]
    #[Assert\Date]
    private \DateTime $updatedAt;

    public function __construct(
        string $title,
        string $author,
        ?string $isbn = null,
        ?DateTimeImmutable $publishedAt = null,
    ) {
        $this->validateTitle($title);
        $this->validateAuthor($author);
        $this->isbn = $isbn;
        $this->publishedAt = $publishedAt ?? new \DateTimeImmutable();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    private function validateTitle(string $title): void
    {
        if (empty(trim($title))) {
            throw new InvalidArgumentException('Le titre ne peut pas être vide');
        } else {
            $this->title = $title;
        }
    }
    
    private function validateAuthor(string $author): void
    {
        if (empty(trim($author))) {
            throw new InvalidArgumentException('L\'auteur ne peut pas être vide');
        } else {
            $this->author = $author;
        }
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }
    
    public function getAuthor(): string
    {
        return $this->author;
    }

    public function setAuthor(string $author): void
    {
        $this->author = $author;
    }

    public function getIsbn(): string
    {
        return $this->isbn;
    }

    public function setIsbn(string $isbn): void
    {
        $this->isbn = $isbn;
    }

    public function getPublishedAt(): \DateTimeInterface
    {
        return $this->publishedAt;
    }

    public function setPublishedAt(\DateTimeInterface $publishedAt): void
    {
        $this->publishedAt = $publishedAt;
    }
    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return $this->updatedAt;
    }

    /**
     * Converts the book entity to an array for API responses
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'author' => $this->author,
            'isbn' => $this->isbn,
            'publishedAt' => $this->publishedAt->format('Y-m-d H:i:s'),
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt->format('Y-m-d H:i:s')
        ];
    }
}
