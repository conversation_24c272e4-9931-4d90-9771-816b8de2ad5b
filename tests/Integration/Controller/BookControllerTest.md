<?php

// tests/Integration/Controller/BookControllerTest.php
namespace App\Tests\Integration\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class BookControllerTest extends WebTestCase
{
    public function testCreateBookSuccess(): void
    {
        $client = static::createClient();

        $client->request('POST', '/api/books', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'title' => 'Le Petit Prince',
            'author' => '<PERSON>',
            'isbn' => '978-2-07-040857-4',
            'publishedAt' => '1943-04-06',
            'pages' => 96
        ]));

        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('id', $response);
        $this->assertEquals('<PERSON>', $response['title']);
        $this->assertEquals('<PERSON>', $response['author']);
    }

    public function testCreateBookValidationError(): void
    {
        $client = static::createClient();

        $client->request('POST', '/api/books', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'author' => 'Victor Hugo'
            // Missing title
        ]));

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertArrayHasKey('errors', $response);
    }

    public function testCreateBookDuplicateIsbn(): void
    {
        $client = static::createClient();

        // Create first book
        $client->request('POST', '/api/books', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'title' => 'First Book',
            'author' => 'First Author',
            'isbn' => '978-2-07-040857-4'
        ]));

        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);

        // Try to create second book with same ISBN
        $client->request('POST', '/api/books', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'title' => 'Second Book',
            'author' => 'Second Author',
            'isbn' => '978-2-07-040857-4'
        ]));

        $this->assertResponseStatusCodeSame(Response::HTTP_CONFLICT);
    }

    public function testGetAllBooks(): void
    {
        $client = static::createClient();

        $client->request('GET', '/api/books');

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('Content-Type', 'application/json');
        
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
    }

    public function testGetBooksWithAuthorFilter(): void
    {
        $client = static::createClient();

        // Create test books
        $this->createTestBook($client, 'Book 1', 'Hugo');
        $this->createTestBook($client, 'Book 2', 'Orwell');
        $this->createTestBook($client, 'Book 3', 'Hugo');

        $client->request('GET', '/api/books?author=Hugo');

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertCount(2, $response);
        
        foreach ($response as $book) {
            $this->assertStringContainsStringIgnoreCase('Hugo', $book['author']);
        }
    }

    public function testGetBooksWithDateFilter(): void
    {
        $client = static::createClient();

        // Create test books
        $this->createTestBookWithDate($client, 'Old Book', 'Author 1', '2020-01-01');
        $this->createTestBookWithDate($client, 'New Book', 'Author 2', '2023-01-01');

        $client->request('GET', '/api/books?publishedAfter=2022-01-01');

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertCount(1, $response);
        $this->assertEquals('New Book', $response[0]['title']);
    }

    public function testGetBooksWithCombinedFilters(): void
    {
        $client = static::createClient();

        // Create test books
        $this->createTestBookWithDate($client, 'Hugo Old', 'Hugo', '2020-01-01');
        $this->createTestBookWithDate($client, 'Hugo New', 'Hugo', '2023-01-01');
        $this->createTestBookWithDate($client, 'Orwell New', 'Orwell', '2023-01-01');

        $client->request('GET', '/api/books?author=Hugo&publishedAfter=2022-01-01');

        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        
        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertCount(1, $response);
        $this->assertEquals('Hugo New', $response[0]['title']);
    }

    private function createTestBook($client, string $title, string $author): void
    {
        $client->request('POST', '/api/books', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'title' => $title,
            'author' => $author
        ]));
    }

    private function createTestBookWithDate($client, string $title, string $author, string $date): void
    {
        $client->request('POST', '/api/books', [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], json_encode([
            'title' => $title,
            'author' => $author,
            'publishedAt' => $date
        ]));
    }
}
