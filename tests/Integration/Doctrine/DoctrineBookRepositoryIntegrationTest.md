<?php

namespace App\Tests\Integration\Doctrine;

use App\BookStore\Infrastructure\Doctrine\DoctrineBookRepository;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\NullOutput;

/**
 * Class DoctrineBookRepositoryIntegrationTest
 *
 *
 * @package App\Tests\Integration\Doctrine
 * @group integration
 * @group doctrine
 * @group repository
 * @group book
 * @group book-repository
 * @group book-repository-integration
 *
 */
class DoctrineBookRepositoryIntegrationTest extends KernelTestCase
{
    private EntityManagerInterface $em;

    public static function setUpBeforeClass(): void
    {
        static::bootKernel();

        (new Application(static::$kernel))
            ->find('doctrine:database:create')
            ->run(new ArrayInput(['--if-not-exists' => true]), new NullOutput());

        (new Application(static::$kernel))
            ->find('doctrine:schema:update')
            ->run(new ArrayInput(['--force' => true]), new NullOutput());
    }

    protected function setUp(): void
    {
        $this->em = static::getContainer()->get(EntityManagerInterface::class);
        $this->em->getConnection()->executeStatement('TRUNCATE book');
    }
}
