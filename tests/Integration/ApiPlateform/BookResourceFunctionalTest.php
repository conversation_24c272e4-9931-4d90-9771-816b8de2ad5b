<?php

declare(strict_types=1);

namespace App\Tests\Integration\ApiPlatform;

use ApiPlatform\Symfony\Bundle\Test\ApiTestCase;
use App\BookStore\Domain\Model\Book;
use App\BookStore\Domain\Repository\BookRepositoryInterface;
use App\BookStore\Domain\ValueObject\BookId;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Component\Uid\Uuid;

/**
 * Functional test for the Book API resource.
 *
 * This test simulates a client interacting with the /api/books endpoint.
 * It verifies that creating a book via a POST request correctly persists the entity
 * and that it can be retrieved via a GET request.
 */
class BookResourceFunctionalTest extends ApiTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        $kernel = self::bootKernel();
        $application = new Application($kernel);
        $application->setAutoExit(false);

        // Drop and create the schema
        $input = new ArrayInput([
            'command' => 'doctrine:schema:drop',
            '--force' => true,
        ]);
        $application->run($input, new BufferedOutput());

        $input = new ArrayInput([
            'command' => 'doctrine:schema:create',
        ]);
        $application->run($input, new BufferedOutput());
    }

    public function testPostAndGetBook(): void
    {
        $client = static::createClient();

        // 1. Create a book
        $response = $client->request('POST', '/api/books', [
            'json' => [
                'name' => 'The Lord of the Rings',
                'description' => 'The greatest fantasy book ever written.',
                'author' => 'J.R.R. Tolkien',
                'content' => 'In a hole in the ground there lived a hobbit.',
                'price' => 2500, // in cents
            ],
            'headers' => [
                'Content-Type' => 'application/ld+json',
                'Accept' => 'application/ld+json',
            ],
        ]);
  
        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(201);
        $this->assertJsonContains([
            'name' => 'The Lord of the Rings',
            'description' => 'The greatest fantasy book ever written.',
            'author' => 'J.R.R. Tolkien',
            'content' => 'In a hole in the ground there lived a hobbit.',
            'price' => 2500,
        ]);
 
        $data = $response->toArray();
        
        $bookIdValue = $data['id'];
    
        $bookId = BookId::fromString($bookIdValue);

        // 2. Verify persistence with the repository
        /** @var BookRepositoryInterface $bookRepository */
        $bookRepository = static::getContainer()->get(BookRepositoryInterface::class);
        $book = $bookRepository->ofId($bookId);

        $this->assertInstanceOf(Book::class, $book);
        $this->assertTrue($book->id()->equals($bookId));
        $this->assertSame('The Lord of the Rings', $book->name()->value);
        $this->assertSame(2500, $book->price()->amount);

        // 3. Get the book via API
        $client->request('GET', '/api/books/' . $bookIdValue);

        $this->assertResponseIsSuccessful();
        $this->assertJsonContains([
            'id' => $bookIdValue,
            'name' => 'The Lord of the Rings',
            'description' => 'The greatest fantasy book ever written.',
            'author' => 'J.R.R. Tolkien',
            'content' => 'In a hole in the ground there lived a hobbit.',
            'price' => 2500,
        ]);
    }
}
