#!/bin/sh
set -e

# Attendre que la base de données soit prête
echo "En attente de la base de données..."
until php bin/console doctrine:query:sql "SELECT 1" >/dev/null 2>&1; do
  sleep 1
done
echo "Base de données prête."

# Exécuter les commandes de test
echo "Mise en place de la base de données de test..."
php bin/console doctrine:database:drop --force --if-exists --env=test
php bin/console doctrine:database:create --env=test
php bin/console doctrine:migrations:migrate --no-interaction --env=test
echo "Base de données de test mise en place."

# Lancer PHPUnit
echo "Lancement de PHPUnit..."
./vendor/bin/phpunit